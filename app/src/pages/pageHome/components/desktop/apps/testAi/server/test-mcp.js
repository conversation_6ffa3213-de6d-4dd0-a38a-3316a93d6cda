import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js";

async function testMcpServer() {
	console.log("Testing MCP Server...");

	try {
		// Create client
		const client = new Client({
			name: "test-mcp-client",
			version: "1.0.0"
		});

		// Create transport
		const transport = new StreamableHTTPClientTransport(new URL("http://localhost:5004/mcp"));

		// Connect
		console.log("Connecting to MCP server...");
		await client.connect(transport);
		console.log("✅ Connected successfully!");

		// List tools
		console.log("\nListing tools...");
		const tools = await client.listTools();
		console.log("Available tools:", tools.tools?.map(t => t.name) || []);

		// List resources
		console.log("\nListing resources...");
		const resources = await client.listResources();
		console.log("Available resources:", resources.resources?.map(r => r.name) || []);

		// List prompts
		console.log("\nListing prompts...");
		const prompts = await client.listPrompts();
		console.log("Available prompts:", prompts.prompts?.map(p => p.name) || []);

		// Test echo tool
		if (tools.tools?.some(t => t.name === "echo")) {
			console.log("\nTesting echo tool...");
			const result = await client.callTool({
				name: "echo",
				arguments: { message: "Hello from test script!" }
			});
			console.log("Echo tool result:", result.content?.[0]?.text || "No response");
		}

		// Test greeting resource
		console.log("\nTesting greeting resource...");
		try {
			const resource = await client.readResource({
				uri: "greeting://TestUser"
			});
			console.log("Greeting resource result:", resource.contents?.[0]?.text || "No content");
		} catch (error) {
			console.log("Greeting resource error:", error.message);
		}

		// Test chat prompt
		if (prompts.prompts?.some(p => p.name === "chat")) {
			console.log("\nTesting chat prompt...");
			const prompt = await client.getPrompt({
				name: "chat",
				arguments: { message: "Hello from test!" }
			});
			console.log("Chat prompt result:", prompt.messages?.length || 0, "messages");
		}

		// Close connection
		await client.close();
		console.log("\n✅ All tests completed successfully!");

	} catch (error) {
		console.error("❌ Test failed:", error.message);
		console.error("Stack trace:", error.stack);
	}
}

// Run the test
testMcpServer().catch(console.error);
