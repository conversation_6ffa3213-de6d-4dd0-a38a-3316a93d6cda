import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js";

async function finalTest() {
	try {
		console.log("🎯 Final test with real Gemini API...");
		
		const client = new Client({ name: "final-test", version: "1.0.0" });
		const transport = new StreamableHTTPClientTransport(new URL("http://localhost:5004/mcp"));
		
		await client.connect(transport);
		console.log("✅ Connected!");
		
		// Test AI chat with a joke request
		console.log("\n🤖 Testing: 'tell me a joke'");
		const result = await client.callTool({
			name: "ai_chat",
			arguments: { message: "tell me a joke" }
		});
		
		console.log("🎉 AI Response:", result.content?.[0]?.text || "No response");
		
		await client.close();
		
	} catch (error) {
		console.error("❌ Error:", error.message);
	}
}

finalTest();
