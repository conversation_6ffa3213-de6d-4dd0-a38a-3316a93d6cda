import { randomUUID } from "node:crypto";
import { McpServer, ResourceTemplate } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StreamableHTTPServerTransport } from "@modelcontextprotocol/sdk/server/streamableHttp.js";
import { isInitializeRequest } from "@modelcontextprotocol/sdk/types.js";
import cors from "cors";
import express from "express";
import { z } from "zod";
import OpenAI from "openai";

import config from "./../../../../../../../config.json" with { type: "json" };

// ANSI color codes
const _colors = {
	green: "\x1b[32m",
	red: "\x1b[31m",
	reset: "\x1b[0m",
};

const PORT = config.serverAi.port;

// Initialize OpenAI client (you'll need to set your API key)
const openai = new OpenAI({
	apiKey: process.env.OPENAI_API_KEY || "demo-key", // Set this in your environment
	baseURL: process.env.OPENAI_BASE_URL, // Optional: for local AI like Ollama
});

// Check if we have a real API key
const hasValidApiKey = process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== "demo-key";

// Create Express app
const app = express();
app.use(express.json());

// Configure CORS for browser-based MCP clients
app.use(
	cors({
		origin: "*", // Configure appropriately for production
		exposedHeaders: ["Mcp-Session-Id"],
		allowedHeaders: ["Content-Type", "mcp-session-id", "mcp-protocol-version", "Authorization", "Accept"],
	}),
);

// Map to store transports by session ID
const transports = {};

// Create MCP server instance
function createMcpServer() {
	const mcpServer = new McpServer({
		name: "testAi-mcp-server",
		version: "1.0.0",
	});

	// Register AI chat tool
	mcpServer.registerTool(
		"ai_chat",
		{
			title: "AI Chat",
			description: "Chat with AI using OpenAI GPT",
			inputSchema: z.object({
				message: z.string().describe("The message to send to the AI"),
				model: z.string().optional().describe("AI model to use (default: gpt-3.5-turbo)")
			}),
		},
		async ({ message, model = "gpt-3.5-turbo" }) => {
			console.log(`${_colors.green}AI Chat called with message: ${message}${_colors.reset}`);

			// Check if we have a valid API key
			if (!hasValidApiKey) {
				const demoResponse = `🤖 Demo AI Response: I received your message "${message}". To get real AI responses, please set your OPENAI_API_KEY environment variable. You can get one from https://platform.openai.com/api-keys`;
				console.log(`${_colors.red}Demo response (no API key)${_colors.reset}`);
				return {
					content: [{ type: "text", text: demoResponse }],
				};
			}

			try {
				const completion = await openai.chat.completions.create({
					model: model,
					messages: [
						{
							role: "system",
							content: "You are a helpful AI assistant integrated into an MCP server. Be concise and helpful."
						},
						{
							role: "user",
							content: message
						}
					],
					max_tokens: 500,
					temperature: 0.7
				});

				const response = completion.choices[0]?.message?.content || "Sorry, I couldn't generate a response.";
				console.log(`${_colors.red}AI responding with: ${response.substring(0, 100)}...${_colors.reset}`);

				return {
					content: [{ type: "text", text: response }],
				};
			} catch (error) {
				console.error("OpenAI API error:", error);
				const fallbackResponse = `AI Chat Error: ${error.message}. Please check your OpenAI API key and try again.`;
				return {
					content: [{ type: "text", text: fallbackResponse }],
				};
			}
		},
	);

	// Register code generation tool
	mcpServer.registerTool(
		"generate_code",
		{
			title: "Code Generator",
			description: "Generate code based on description",
			inputSchema: {
				description: z.string().describe("Description of what code to generate"),
				language: z.string().optional().describe("Programming language (default: javascript)")
			},
		},
		async ({ description, language = "javascript" }) => {
			console.log(`${_colors.green}Code generation requested: ${description} (${language})${_colors.reset}`);

			// Check if we have a valid API key
			if (!hasValidApiKey) {
				const demoCode = `// 🤖 Demo Code Generator
// Description: ${description}
// Language: ${language}
//
// This is a demo response. To get real AI-generated code,
// please set your OPENAI_API_KEY environment variable.
// Get one from: https://platform.openai.com/api-keys

function demoFunction() {
    console.log("This is demo ${language} code for: ${description}");
    return "Set up your OpenAI API key for real code generation!";
}`;
				console.log(`${_colors.red}Demo code response (no API key)${_colors.reset}`);
				return {
					content: [{ type: "text", text: demoCode }],
				};
			}

			try {
				const completion = await openai.chat.completions.create({
					model: "gpt-3.5-turbo",
					messages: [
						{
							role: "system",
							content: `You are a code generation assistant. Generate clean, well-commented ${language} code based on the user's description. Only return the code, no explanations.`
						},
						{
							role: "user",
							content: `Generate ${language} code for: ${description}`
						}
					],
					max_tokens: 800,
					temperature: 0.3
				});

				const code = completion.choices[0]?.message?.content || "// Sorry, couldn't generate code";
				console.log(`${_colors.red}Generated code (${code.length} chars)${_colors.reset}`);

				return {
					content: [{ type: "text", text: code }],
				};
			} catch (error) {
				console.error("Code generation error:", error);
				return {
					content: [{ type: "text", text: `// Code generation failed: ${error.message}` }],
				};
			}
		},
	);

	// Register a greeting resource
	mcpServer.registerResource(
		"greeting",
		new ResourceTemplate("greeting://{name}", { list: undefined }),
		{
			title: "Greeting Resource",
			description: "Dynamic greeting generator",
		},
		async (uri, { name }) => {
			console.log(`${_colors.green}Resource requested for name: ${name}${_colors.reset}`);
			const greeting = `Hello, ${name}! Welcome to the MCP testAi server.`;
			console.log(`${_colors.red}Resource responding with: ${greeting}${_colors.reset}`);
			return {
				contents: [
					{
						uri: uri.href,
						text: greeting,
					},
				],
			};
		},
	);

	// Register a conversation prompt
	mcpServer.registerPrompt(
		"chat",
		{
			title: "Chat Prompt",
			description: "Creates a chat prompt with the provided message",
			argsSchema: { message: z.string() },
		},
		({ message }) => {
			console.log(`${_colors.green}Prompt requested with message: ${message}${_colors.reset}`);
			return {
				messages: [
					{
						role: "user",
						content: {
							type: "text",
							text: `Please respond to this message: ${message}`,
						},
					},
				],
			};
		},
	);

	return mcpServer;
}

// Add debug middleware
app.use((req, _res, next) => {
	console.log(`${_colors.green}${req.method} ${req.path}${_colors.reset}`);
	next();
});

// Handle POST requests for client-to-server communication
app.post("/mcp", async (req, res) => {
	console.log(`${_colors.green}POST /mcp received${_colors.reset}`);
	// Check for existing session ID
	const sessionId = req.headers["mcp-session-id"];
	let transport;

	if (sessionId && transports[sessionId]) {
		// Reuse existing transport
		transport = transports[sessionId];
	} else if (!sessionId && isInitializeRequest(req.body)) {
		// New initialization request
		transport = new StreamableHTTPServerTransport({
			sessionIdGenerator: () => randomUUID(),
			onsessioninitialized: (sessionId) => {
				// Store the transport by session ID
				transports[sessionId] = transport;
			},
			// DNS rebinding protection is disabled by default for backwards compatibility
			enableDnsRebindingProtection: false,
		});

		// Clean up transport when closed
		transport.onclose = () => {
			if (transport.sessionId) {
				delete transports[transport.sessionId];
			}
		};

		const mcpServer = createMcpServer();

		// Connect to the MCP server
		await mcpServer.connect(transport);
	} else {
		// Invalid request
		res.status(400).json({
			jsonrpc: "2.0",
			error: {
				code: -32000,
				message: "Bad Request: No valid session ID provided",
			},
			id: null,
		});
		return;
	}

	// Handle the request
	await transport.handleRequest(req, res, req.body);
});

// Reusable handler for GET and DELETE requests
const handleSessionRequest = async (req, res) => {
	const sessionId = req.headers["mcp-session-id"];
	if (!sessionId || !transports[sessionId]) {
		res.status(400).send("Invalid or missing session ID");
		return;
	}

	const transport = transports[sessionId];
	await transport.handleRequest(req, res);
};

// Handle GET requests for server-to-client notifications via SSE
app.get("/mcp", handleSessionRequest);

// Handle DELETE requests for session termination
app.delete("/mcp", handleSessionRequest);

// Catch-all route for debugging
app.use((req, res) => {
	console.log(`${_colors.red}Unhandled route: ${req.method} ${req.originalUrl}${_colors.reset}`);
	res.status(404).json({ error: "Not found" });
});

// Start the server
app.listen(PORT, () => {
	console.log(`MCP Server running on port ${PORT}`);
	console.log(`MCP endpoint available at http://localhost:${PORT}/mcp`);
});

// Handle graceful shutdown
process.on("SIGINT", () => {
	console.log("\nShutting down MCP server...");
	// Close all transports
	Object.values(transports).forEach((transport) => {
		if (transport && typeof transport.close === "function") {
			transport.close();
		}
	});
	process.exit(0);
});
