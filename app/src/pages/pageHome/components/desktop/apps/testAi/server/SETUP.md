# 🤖 AI-Powered MCP Server Setup

Your MCP server now uses **Google Gemini** for AI functionality!

## 🚀 Quick Setup

### 1. Get Your Gemini API Key
1. Go to [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy your API key

### 2. Set Environment Variable
```bash
# Option 1: Set for current session
export GEMINI_API_KEY="your-api-key-here"

# Option 2: Create .env file (recommended)
echo "GEMINI_API_KEY=your-api-key-here" > .env
```

### 3. Restart the Server
```bash
node serverAi.js
```

## 🎯 What You Get

### **AI Chat Tool** (`ai_chat`)
- Real conversations with Gemini AI
- Supports different Gemini models
- Intelligent, context-aware responses

### **Code Generation Tool** (`generate_code`)
- AI-generated code from descriptions
- Supports multiple programming languages
- Clean, well-commented code output

## 🔧 Available Models

- `gemini-1.5-flash` (default) - Fast and efficient
- `gemini-1.5-pro` - More capable for complex tasks
- `gemini-1.0-pro` - Stable version

## 💡 Demo Mode

Without an API key, the server runs in demo mode:
- Shows example responses
- Explains how to set up real AI
- Perfect for testing the MCP integration

## 🌐 Using in Browser

1. Make sure the server is running on port 5004
2. Open your React app at localhost:5000
3. Navigate to the TestAi app
4. Select "Tool" mode and choose between:
   - **💬 AI Chat** - Ask questions, get help
   - **💻 Code Gen** - Describe code, get implementations

## 🔒 Security Note

Keep your API key secure:
- Never commit it to version control
- Use environment variables
- Consider using `.env` files for local development

Enjoy your AI-powered MCP server! 🎉
