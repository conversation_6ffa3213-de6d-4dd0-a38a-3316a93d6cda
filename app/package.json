{"name": "v", "private": true, "version": "1.4.66", "type": "module", "homepage": "https://tambia1.github.io", "scripts": {"init": "npm install --legacy-peer-deps", "start": "vite --host --port 5000", "build": "tsc && vite build", "preview": "vite preview --port 5000", "deploy": "gh-pages -d ./dist", "release": "node ./src/scripts/release.js", "biome": "biome check .", "biome:format": "biome format --write .", "biome:fix": "biome check --write .", "env:dev": "cross-env ENV=dev", "env:qa": "cross-env ENV=qa", "env:prod": "cross-env ENV=prod", "test:units": "vitest", "test:coverage": "vitest watch --coverage", "test:visuals": "playwright test", "test:visuals:show": "playwright test --headed", "test:visuals:debug": "playwright test --debug", "test:visuals:manual": "playwright test --ui", "test:visuals:update": "playwright test --update-snapshots", "test:visuals:codegen": "playwright codegen --viewport-size=390,844", "test:visuals:reporter": "playwright test --reporter=list"}, "dependencies": {"@apollo/client": "3.13.8", "@dotenvx/dotenvx": "1.47.3", "@google/generative-ai": "^0.24.1", "@modelcontextprotocol/sdk": "^1.15.1", "@react-oauth/google": "0.12.2", "@stylexjs/stylex": "0.14.1", "@tanstack/react-query-devtools": "5.82.0", "@types/cors": "^2.8.19", "@types/express": "5.0.3", "cors": "^2.8.5", "date-fns": "4.1.0", "express": "^5.1.0", "face-api.js": "^0.22.2", "https": "1.0.0", "i18next": "25.3.2", "i18next-browser-languagedetector": "8.2.0", "localforage": "1.10.0", "react": "19.1.0", "react-dom": "19.1.0", "react-i18next": "15.6.0", "react-modal": "3.16.3", "react-router-dom": "7.6.3", "react-svg": "16.3.0", "redis": "5.6.0", "styled-components": "6.1.19", "zod": "4.0.5", "zustand": "5.0.6"}, "devDependencies": {"@biomejs/biome": "2.1.1", "@originjs/vite-plugin-federation": "1.4.1", "@playwright/test": "1.54.1", "@tanstack/react-query": "^5.82.0", "@testing-library/react": "16.3.0", "@testing-library/user-event": "14.6.1", "@types/node": "24.0.13", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/redis": "4.0.11", "@types/styled-components": "5.1.34", "@types/ws": "8.18.1", "@vitejs/plugin-react": "4.6.0", "@vitest/coverage-v8": "3.2.4", "babel-plugin-styled-components": "2.1.4", "cross-env": "7.0.3", "dotenv": "17.2.0", "gh-pages": "6.3.0", "jsdom": "26.1.0", "npm-check-updates": "18.0.1", "playwright": "1.54.1", "typescript": "5.8.3", "vite": "7.0.4", "vite-plugin-checker": "0.10.0", "vite-plugin-stylex": "0.13.0", "vitest": "3.2.4"}}